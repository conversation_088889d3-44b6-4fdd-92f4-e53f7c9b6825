<!DOCTYPE html><html lang="en"><head><meta charset="UTF-8"><meta name="viewport" content="width=device-width, initial-scale=1.0"><title>Tactical Battle Game</title><!-- Bootstrap 5 CSS --><link href="./bootstrap.css" rel="stylesheet"><!-- Google Fonts for Valorant-like UI --><link href="https://fonts.googleapis.com/css2?family=Montserrat:wght@400;500;600;700&display=swap" rel="stylesheet"><link rel="preconnect" href="https://fonts.googleapis.com"><link rel="preconnect" href="https://fonts.gstatic.com" crossorigin><link href="https://fonts.googleapis.com/css2?family=Noto+Color+Emoji&display=swap" rel="stylesheet"><!-- Custom CSS --><link rel="stylesheet" href="styles.css"></head><body><button id="fullscreenButton"><svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M8 3H5a2 2 0 0 0-2 2v3m18 0V5a2 2 0 0 0-2-2h-3m0 18h3a2 2 0 0 0 2-2v-3M3 16v3a2 2 0 0 0 2 2h3"></path></svg> FullScreen </button><!-- Orientation message --><div class="orientation-message"><div><div class="mb-3"><svg xmlns="http://www.w3.org/2000/svg" width="80" height="80" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M4 20h16a2 2 0 0 0 2-2V8a2 2 0 0 0-2-2H4a2 2 0 0 0-2 2v10a2 2 0 0 0 2 2z"></path><path d="M12 6v2"></path></svg></div><p class="mt-2">Please rotate your device to landscape mode</p></div></div><!-- Home Page --><div class="home-container" id="homeScreen"><div class="home-content"><h1 class="game-title">Tactical Battle</h1><p class="game-subtitle">Choose your warriors and conquer the battlefield</p><div class="user-stats"><div class="stat-item"><span class="stat-value" id="coinBalance">0</span><span class="stat-label">Coins</span></div><div class="stat-item"><span class="stat-value" id="unlockedCharacters">3</span><span class="stat-label">Characters</span></div><div class="stat-item"><span class="stat-value" id="completedStages">0</span><span class="stat-label">Stages</span></div><div class="stat-item"><span class="stat-value" id="botCooldown">Open</span><span class="stat-label">Bot Mode</span></div><div class="stats-buttons" style="margin-top: 10px; text-align: center;"><button class="shop-button" id="openShopButton">Shop</button><button class="upgrade-button" id="openUpgradeButton">Upgrade</button><button class="items-button" id="openItemsButton">Items</button><button class="equip-button" id="openEquipButton">Equip Item</button><button class="settings-button" id="openSettingsButton">Settings</button></div></div><div class="game-modes"><div class="mode-card"><h3>Story Mode</h3><p>MY</p><button class="btn" id="storyModeButton">Play Story</button></div><div class="mode-card"><h3>Custom Mode</h3><p>SON</p><button class="btn" id="customModeButton">Play Custom</button></div><div class="mode-card"><h3>Bot Challenge</h3><p>BLACK</p><button class="btn" id="botModeButton">Play Bot</button></div><div class="mode-card"><h3>Infinite Mode</h3><p>ENDLESS</p><button class="btn" id="infiniteModeButton">Play Infinite</button></div></div></div></div><!-- Shop Screen --><div class="shop-container" id="shopScreen"><div class="shop-content"><div class="shop-header"><h2 class="shop-title">Shop</h2><div class="shop-balance"><span>Balance:</span><span id="shopCoinBalance">0</span><span>Coins</span></div><button class="shop-close" id="closeShopButton">&times;</button></div><!-- Shop content --><div class="shop-items" id="shopItems"><!-- Character items will be generated dynamically --></div></div></div><!-- Upgrade Screen --><div class="upgrade-container" id="upgradeScreen"><div class="upgrade-content"><div class="upgrade-header"><h2 class="upgrade-title">Character Upgrades</h2><div class="upgrade-balance"><span>Balance:</span><span id="upgradeCoinBalance">0</span><span>Coins</span></div><button class="upgrade-close" id="closeUpgradeButton">&times;</button></div><!-- Upgrade content --><div class="upgrade-items" id="upgradeItems"><!-- Character upgrade items will be generated dynamically --></div></div></div><!-- Story Mode Screen --><div class="story-container" id="storyScreen"><div class="story-header"><h2 class="story-title">Story Mode</h2><button class="story-back" id="storyBackButton">Back to Home</button></div><div class="difficulty-tabs"><button class="difficulty-tab" data-difficulty="easy">Easy</button><button class="difficulty-tab" data-difficulty="normal">Normal</button><button class="difficulty-tab" data-difficulty="hard">Hard</button><button class="difficulty-tab" data-difficulty="zeed">Zeed</button><button class="difficulty-tab" data-difficulty="brainrot">Brainrot</button></div><div class="stages-grid" id="stagesGrid"><!-- Stages will be generated dynamically --></div></div><!-- Settings Screen --><div class="settings-container" id="settingsScreen"><div class="settings-content"><div class="settings-header"><h2 class="settings-title">Settings</h2><button class="settings-close" id="closeSettingsButton">&times;</button></div><div class="settings-section"><h3 class="settings-section-title">Game Data</h3><div class="settings-button-group"><button class="settings-button" id="exportDataButton">Export Data</button><button class="settings-button" id="importDataButton">Import Data</button></div><textarea id="gameDataField" class="settings-input" rows="5" placeholder="Paste game data here to import"></textarea></div><div class="settings-section"><h3 class="settings-section-title">Reset Game</h3><button class="settings-button" id="forceFixButton" style="background: #ff4655; color: #fff; font-weight: bold; margin-top: 8px;">Reset All Progress</button></div></div></div><!-- Items Screen --><div class="items-container" id="itemsScreen"><div class="items-content"><div class="items-header"><h2 class="items-title">Items Shop</h2><div class="items-balance"><span>Balance:</span><span id="itemsCoinBalance">0</span><span>Coins</span></div><button class="items-close" id="closeItemsButton">&times;</button></div><div class="items-section"><div class="items-content-area" id="itemsContent"><!-- Items will be generated dynamically --></div></div></div></div><!-- Equip Item Screen --><div class="equip-container" id="equipScreen"><div class="equip-content"><div class="equip-header"><h2 class="equip-title">Equip Item</h2><button class="equip-close" id="closeEquipButton">&times;</button></div><div class="equip-section"><div class="equip-content-area" id="equipContent"><!-- Equip UI will be generated dynamically --></div></div></div></div><!-- Game content --><div class="game-content" id="gameScreen" style="display: none;"><!-- Game mode info and back button --><div class="game-header"><button class="back-button" id="backToHomeButton"><svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M19 12H5M12 19l-7-7 7-7"/></svg> Back </button><div class="game-mode-info" id="gameModeInfo">Custom Mode</div><div class="coin-display" id="inGameCoinDisplay"><span id="inGameCoinBalance">0</span><span>Coins</span></div></div><div class="game-wrapper"><div class="row g-2" style="height: 70vh;"><div class="col-2"><div class="player-area" id="player1"><h2>Player 1</h2><div class="character-selection" id="player1-characters"></div></div></div><div class="col-8 battlefield-container"><div class="battlefield" id="battlefield"><!-- Result screen overlay --><div class="result-container" id="resultScreen"><h2 class="result-title" id="resultTitle">Victory!</h2><p class="result-subtitle" id="resultSubtitle">You have defeated your opponent!</p><p class="result-reward" id="resultReward">+50 Coins</p><div class="result-buttons"><button class="btn" id="continueButton">Continue</button><button class="btn" id="replayButton">Play Again</button></div></div></div></div><div class="col-2"><div class="player-area" id="player2"><h2>Player 2</h2><div class="character-selection" id="player2-characters"></div></div></div></div><div class="row mt-2"><div class="col-12 control-area"><div class="d-flex justify-content-center"><button class="btn" id="startButton">Start Battle!</button></div><div class="status" id="status">Select your units!</div></div></div></div></div><!-- Bootstrap 5 JS Bundle with Popper --><script src="./bootstrap.js"></script><script src="./script.js"></script></body></html>