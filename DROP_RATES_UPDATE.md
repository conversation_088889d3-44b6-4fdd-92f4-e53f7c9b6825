# การอัปเดต: เพิ่มการแสดงเรทโอกาสในหน้า Shop และ Items

## สิ่งที่เพิ่มเข้ามา

### 1. ฟังก์ชันคำนวณเรทโอกาส

#### สำหรับตัวละคร (Characters)
- `calculateCharacterDropRates()`: คำนวณเรทโอกาสการได้รับตัวละครแต่ละตัว
- ใช้สูตร: `weight = Math.max(1, Math.floor(1000 / price))`
- ตัวละครที่ราคาแพงกว่าจะมีโอกาสได้น้อยกว่า
- แสดงสถานะว่าปลดล็อกแล้วหรือยัง (✓ สำหรับตัวที่ปลดล็อกแล้ว)

#### สำหรับไอเท็ม (Items)
- `calculateItemDropRates()`: คำนวณเรทโอกาสการได้รับไอเท็มแต่ละชิ้น
- ใช้สูตร: `weight = Math.max(1, Math.floor(500 / price))`
- แสดงจำนวนที่มีอยู่ในวงเล็บ เช่น "Sword (2)"

### 2. การแสดงผลในหน้า Shop

#### ตารางแสดงเรทโอกาส
- แสดงรายการตัวละครทั้งหมดที่สามารถสุ่มได้
- แสดงไอคอน, ชื่อ, ราคา, และเปอร์เซ็นต์โอกาส
- เรียงลำดับจากโอกาสสูงไปต่ำ
- ใช้สีเขียวสำหรับตัวที่ปลดล็อกแล้ว

#### ข้อมูลที่แสดง
- ไอคอนตัวละคร
- ชื่อตัวละคร
- เปอร์เซ็นต์โอกาส
- สีเขียวสำหรับตัวที่ปลดล็อกแล้ว

### 3. การแสดงผลในหน้า Items

#### ตารางแสดงเรทโอกาส
- แสดงรายการไอเท็มทั้งหมดที่สามารถสุ่มได้
- แสดงไอคอน, ชื่อ, และเปอร์เซ็นต์โอกาส
- เรียงลำดับจากโอกาสสูงไปต่ำ
- ใช้สีเขียวสำหรับไอเท็มที่มีอยู่แล้ว

#### ข้อมูลที่แสดง
- ไอคอนไอเท็ม
- ชื่อไอเท็ม
- เปอร์เซ็นต์โอกาส
- สีเขียวสำหรับไอเท็มที่มีอยู่แล้ว

### 4. การจัดแต่ง CSS

#### สไตล์ตารางเรท
- `.drop-rates-section`: กรอบหลักของตารางเรท
- `.drop-rates-table`: ตารางแสดงรายการ
- `.drop-rate-row`: แถวแต่ละรายการ
- `.character-info`, `.item-info`: ข้อมูลตัวละคร/ไอเท็ม
- `.drop-rate-percentage`: เปอร์เซ็นต์โอกาส

#### สีสถานะ
- สีเขียว: ตัวละคร/ไอเท็มที่มีอยู่แล้ว
- สีเทา: ตัวละคร/ไอเท็มที่ยังไม่มี

#### Responsive Design
- ปรับขนาดสำหรับมือถือ
- เปลี่ยนเป็นแนวตั้งในหน้าจอเล็ก

## สูตรการคำนวณ

### ตัวละคร
```javascript
weight = Math.max(1, Math.floor(1000 / price))
dropRate = (weight / totalWeight) * 100
```

### ไอเท็ม
```javascript
weight = Math.max(1, Math.floor(500 / price))
dropRate = (weight / totalWeight) * 100
```

## ตัวอย่างเรท

### ตัวละครราคา 90 เหรียญ
- Weight = Math.floor(1000 / 90) = 11
- โอกาสสูงกว่าตัวละครราคา 1000 เหรียญ

### ตัวละครราคา 1000 เหรียญ
- Weight = Math.floor(1000 / 1000) = 1
- โอกาสต่ำที่สุด

### ไอเท็มราคา 100 เหรียญ
- Weight = Math.floor(500 / 100) = 5
- โอกาสสูงกว่าไอเท็มราคา 600 เหรียญ

### ไอเท็มราคา 600 เหรียญ
- Weight = Math.floor(500 / 600) = 1
- โอกาสต่ำที่สุด

## การใช้งาน

1. เปิดหน้า Shop จะเห็นตารางเรทโอกาสตัวละครด้านล่างปุ่ม Random Unit
2. เปิดหน้า Items จะเห็นตารางเรทโอกาสไอเท็มด้านล่างปุ่ม Random Item
3. ตารางจะอัปเดตอัตโนมัติเมื่อมีการเปลี่ยนแปลงสถานะเกม
4. สีของแถวจะเปลี่ยนตามสถานะการปลดล็อก/การมีอยู่

## หมายเหตุ

- เรทโอกาสจะเปลี่ยนแปลงตามตัวละครที่สามารถสุ่มได้ในขณะนั้น
- ตัวละคร tungtung จะปรากฏในรายการเมื่อผ่าน Brainrot Stage 5 แล้วเท่านั้น
- การแสดงผลรองรับทั้งไอคอน emoji และรูปภาพ
- ไม่แสดงราคาและจำนวนที่มี เพื่อความเรียบง่าย
- ใช้สีเขียวเป็นตัวบ่งชี้หลักสำหรับสิ่งที่มีอยู่แล้ว
